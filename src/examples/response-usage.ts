/**
 * @fileoverview Examples of using the standardized SendResponse function
 * This file demonstrates the new response pattern for API endpoints
 */

import { z } from 'zod';

import { SendResponse, create<PERSON><PERSON><PERSON>, ResponseHelpers } from '@/utils/handler';

// Example 1: Basic usage with SendResponse
const userLoginSchema = {
  request: {
    body: z.object({
      email: z.string().email(),
      password: z.string().min(8)
    })
  },
  response: {
    body: z.object({
      code: z.literal('SUCCESS'),
      message: z.string(),
      result: z.object({
        token: z.string(),
        id: z.string(),
        email: z.string(),
        name: z.string()
      })
    })
  }
} as const;

export const handleUserLogin = createHandler(
  userLoginSchema,
  async (req, res) => {
    // Your business logic here...

    const result = {
      token: 'jwt-token-here',
      id: '123',
      email: '<EMAIL>',
      name: 'Test User'
    };

    // Using SendResponse - automatically handles code and message
    SendResponse(res, 'Login successful', result);

    // This will return:
    // {
    //   "code": "SUCCESS",
    //   "message": "Login successful",
    //   "result": {
    //     "token": "jwt-token-here",
    //     "id": "123",
    //     "email": "<EMAIL>",
    //     "name": "Test User"
    //   }
    // }
  }
);

// Example 2: Using ResponseHelpers for different status codes
const createUserSchema = {
  request: {
    body: z.object({
      name: z.string(),
      email: z.string().email()
    })
  },
  response: {
    body: z.object({
      code: z.literal('CREATED'),
      message: z.string(),
      result: z.object({
        id: z.string(),
        name: z.string(),
        email: z.string()
      })
    })
  }
} as const;

export const handleCreateUser = createHandler(
  createUserSchema,
  async (req, res) => {
    // Your business logic here...

    const result = {
      id: '456',
      name: 'New User',
      email: '<EMAIL>'
    };

    // Using ResponseHelpers.created for 201 status
    ResponseHelpers.created(res, 'User created successfully', result);

    // This will return:
    // {
    //   "code": "CREATED",
    //   "message": "User created successfully",
    //   "result": {
    //     "id": "456",
    //     "name": "New User",
    //     "email": "<EMAIL>"
    //   }
    // }
  }
);

// Example 3: Different response types
export const handleUpdateUser = createHandler(async (req, res) => {
  const result = { id: '123', updated: true };
  ResponseHelpers.updated(res, 'User updated successfully', result);
});

export const handleDeleteUser = createHandler(async (req, res) => {
  const result = { id: '123', deleted: true };
  ResponseHelpers.deleted(res, 'User deleted successfully', result);
});

export const handleNoContent = createHandler(async (req, res) => {
  ResponseHelpers.noContent(res, 'Operation completed');
});

// Example 4: Custom success code
export const handleCustomResponse = createHandler(async (req, res) => {
  const result = { status: 'processing' };
  SendResponse(res, 'Request accepted for processing', result, 'ACCEPTED');
});
